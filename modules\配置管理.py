import os
import json
import configparser
from typing import Dict, Any, List, Optional

class 配置管理器:
    def __init__(self, 配置文件: str = '闲鱼配置.ini'):
        self.配置文件名 = 配置文件
        self.配置 = configparser.ConfigParser()
        self.配置目录 = None
        self.配置文件 = None
        
    def 设置配置目录(self, 配置目录: str) -> None:
        """设置配置文件保存目录"""
        self.配置目录 = 配置目录
        self.配置文件 = os.path.join(self.配置目录, self.配置文件名)
        
        # 确保配置目录存在
        if not os.path.exists(self.配置目录):
            os.makedirs(self.配置目录)
            
        self.加载配置()
        
    def 加载配置(self) -> None:
        """加载配置文件"""
        if self.配置文件 and os.path.exists(self.配置文件):
            try:
                self.配置.read(self.配置文件, encoding='utf-8')
                print(f"成功加载配置文件: {self.配置文件}")
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self.创建默认配置()
                self.保存配置()
        else:
            print(f"配置文件不存在，创建默认配置: {self.配置文件}")
            self.创建默认配置()
            if self.配置文件:
                self.保存配置()
            
    def 创建默认配置(self) -> None:
        """创建默认配置"""
        self.配置['系统设置'] = {
            '用户代理': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            '应用密钥': '12574478',
            '接口名称': 'mtop.taobao.idle.shop.user.items',
            '基础url': 'https://h5api.m.taobao.com/h5/mtop.taobao.idle.shop.user.items/1.0/'
        }
        
        self.配置['默认设置'] = {
            '默认卖家id': '3800476293',
            '默认卖家名称': '小茶',
            '默认分组id': '51959993',
            '默认分组名称': '综合'
        }
            
    def 保存配置(self) -> bool:
        """保存配置到文件"""
        if not self.配置文件:
            print("配置文件路径未设置，无法保存")
            return False
            
        try:
            # 确保配置目录存在
            if not os.path.exists(self.配置目录):
                os.makedirs(self.配置目录)
                
            with open(self.配置文件, 'w', encoding='utf-8') as f:
                self.配置.write(f)
            print(f"配置已保存到: {self.配置文件}")
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
            
    def 获取系统设置(self) -> Dict[str, str]:
        """获取系统设置"""
        if '系统设置' not in self.配置:
            self.创建默认配置()
            self.保存配置()
            
        return {
            '用户代理': self.配置['系统设置'].get('用户代理'),
            '应用密钥': self.配置['系统设置'].get('应用密钥'),
            '接口名称': self.配置['系统设置'].get('接口名称'),
            '基础URL': self.配置['系统设置'].get('基础url')  # 注意这里：配置文件中是小写url，返回时用大写URL
        }
        
    def 获取所有卖家(self) -> List[Dict[str, str]]:
        """获取所有卖家信息"""
        卖家列表 = []
        
        for 节名称 in self.配置.sections():
            if 节名称.startswith('卖家_'):
                卖家ID = 节名称.split('_')[1]
                
                # 尝试获取卖家名称，兼容不同的大小写配置
                try:
                    # 先尝试标准的"卖家名称"
                    if '卖家名称' in self.配置[节名称]:
                        卖家名称 = self.配置[节名称]['卖家名称']
                    # 再尝试小写的"卖家名称"
                    elif '卖家名称'.lower() in [k.lower() for k in self.配置[节名称]]:
                        for key in self.配置[节名称]:
                            if key.lower() == '卖家名称'.lower():
                                卖家名称 = self.配置[节名称][key]
                                break
                    else:
                        卖家名称 = '未知'
                except:
                    卖家名称 = '未知'
                
                # 打印调试信息
                print(f"找到卖家: {卖家ID}, 名称: {卖家名称}")
                
                卖家信息 = {
                    '卖家ID': 卖家ID,
                    '卖家名称': 卖家名称
                }
                卖家列表.append(卖家信息)
        
        # 打印总数
        print(f"共找到 {len(卖家列表)} 个卖家")
        return 卖家列表
        
    def 保存卖家信息(self, 卖家ID: str, 卖家名称: str) -> bool:
        """保存卖家信息到配置文件"""
        节名称 = f'卖家_{卖家ID}'
        
        if 节名称 not in self.配置:
            self.配置[节名称] = {}
            
        self.配置[节名称]['卖家ID'] = 卖家ID
        self.配置[节名称]['卖家名称'] = 卖家名称
        
        try:
            return self.保存配置()
        except Exception as e:
            print(f"保存卖家信息失败: {e}")
            return False
            
    def 删除卖家信息(self, 卖家ID: str) -> bool:
        """从配置文件中删除卖家信息"""
        节名称 = f'卖家_{卖家ID}'
        
        if 节名称 in self.配置:
            self.配置.remove_section(节名称)
            
            try:
                return self.保存配置()
            except Exception as e:
                print(f"删除卖家信息失败: {e}")
                return False
        
        return False
        
    def 获取默认设置(self) -> Dict[str, str]:
        if "默认设置" not in self.配置:
            self.初始化配置()
            
        return {
            "默认卖家id": self.配置["默认设置"].get("默认卖家id", "3800476293"),
            "默认卖家名称": self.配置["默认设置"].get("默认卖家名称", "小茶"),
            "默认分组id": self.配置["默认设置"].get("默认分组id", "51959993"),
            "默认分组名称": self.配置["默认设置"].get("默认分组名称", "综合")
        }
        
    def 更新默认设置(self, 默认卖家ID: str = None, 默认卖家名称: str = None, 默认分组ID: str = None, 默认分组名称: str = None) -> bool:
        if "默认设置" not in self.配置:
            self.初始化配置()
            
        if 默认卖家ID:
            self.配置["默认设置"]["默认卖家id"] = 默认卖家ID
        if 默认卖家名称:
            self.配置["默认设置"]["默认卖家名称"] = 默认卖家名称
        if 默认分组ID:
            self.配置["默认设置"]["默认分组id"] = 默认分组ID
        if 默认分组名称:
            self.配置["默认设置"]["默认分组名称"] = 默认分组名称
            
        return self.保存配置()
        
    def 获取Cookie(self) -> str:
        try:
            cookie配置文件 = os.path.join(self.配置目录, 'cookie_config.json')
            if os.path.exists(cookie配置文件):
                with open(cookie配置文件, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def 保存Cookie(self, cookie: str) -> bool:
        try:
            cookie配置文件 = os.path.join(self.配置目录, 'cookie_config.json')
            with open(cookie配置文件, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False

    def 获取卖家名称(self, 卖家ID: str) -> str:
        """根据卖家ID获取卖家名称"""
        节名称 = f'卖家_{卖家ID}'
        
        if 节名称 in self.配置:
            return self.配置[节名称].get('卖家名称', '未知卖家')
        
        return '未知卖家'

    def 获取卖家列表(self) -> List[Dict[str, str]]:
        """获取卖家列表"""
        return self.获取所有卖家() 
        
    def 初始化配置(self) -> None:
        """初始化配置"""
        if "默认设置" not in self.配置:
            self.配置["默认设置"] = {
                "默认卖家id": "3800476293",
                "默认卖家名称": "小茶",
                "默认分组id": "51959993",
                "默认分组名称": "综合"
            }
            self.保存配置()